package mcp

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ProjectCommentsApi struct{}

// CreateProjectComment 创建项目评论
// @Tags ProjectComments
// @Summary 创建项目评论
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.CreateProjectCommentRequest true "创建项目评论"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /projectComments/createProjectComment [post]
func (api *ProjectCommentsApi) CreateProjectComment(c *gin.Context) {
	var req request.CreateProjectCommentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userId := utils.GetUserID(c)
	if userId == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	// 创建评论
	if err := projectCommentsService.CreateProjectComment(c.Request.Context(), &req, userId); err != nil {
		global.GVA_LOG.Error("创建评论失败!", zap.Error(err))
		response.FailWithMessage("创建评论失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("评论创建成功", c)
}

// UpdateProjectComment 更新项目评论
// @Tags ProjectComments
// @Summary 更新项目评论
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.UpdateProjectCommentRequest true "更新项目评论"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /projectComments/updateProjectComment [put]
func (api *ProjectCommentsApi) UpdateProjectComment(c *gin.Context) {
	var req request.UpdateProjectCommentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userId := utils.GetUserID(c)
	if userId == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	// 更新评论
	if err := projectCommentsService.UpdateProjectComment(c.Request.Context(), &req, userId); err != nil {
		global.GVA_LOG.Error("更新评论失败!", zap.Error(err))
		response.FailWithMessage("更新评论失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("评论更新成功", c)
}

// DeleteProjectComment 删除项目评论
// @Tags ProjectComments
// @Summary 删除项目评论
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param commentID path uint true "评论ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /projectComments/deleteProjectComment/{commentID} [delete]
func (api *ProjectCommentsApi) DeleteProjectComment(c *gin.Context) {
	commentIDStr := c.Param("commentID")
	commentID, err := strconv.ParseUint(commentIDStr, 10, 64)
	if err != nil {
		response.FailWithMessage("无效的评论ID", c)
		return
	}

	// 获取当前用户ID
	userId := utils.GetUserID(c)
	if userId == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	// 删除评论
	if err := projectCommentsService.DeleteProjectComment(c.Request.Context(), uint(commentID), userId); err != nil {
		global.GVA_LOG.Error("删除评论失败!", zap.Error(err))
		response.FailWithMessage("删除评论失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("评论删除成功", c)
}

// GetProjectCommentsList 获取项目评论列表
// @Tags ProjectComments
// @Summary 获取项目评论列表
// @Accept application/json
// @Produce application/json
// @Param data body request.ProjectCommentsListRequest true "获取项目评论列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /projectComments/getProjectCommentsList [post]
func (api *ProjectCommentsApi) GetProjectCommentsList(c *gin.Context) {
	var req request.ProjectCommentsListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	list, total, err := projectCommentsService.GetProjectCommentsList(c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("获取评论列表失败!", zap.Error(err))
		response.FailWithMessage("获取评论列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// GetProjectRatingStats 获取项目评分统计
// @Tags ProjectComments
// @Summary 获取项目评分统计
// @Accept application/json
// @Produce application/json
// @Param projectID path uint true "项目ID"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /projectComments/getProjectRatingStats/{projectID} [get]
func (api *ProjectCommentsApi) GetProjectRatingStats(c *gin.Context) {
	projectIDStr := c.Param("projectID")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 64)
	if err != nil {
		response.FailWithMessage("无效的项目ID", c)
		return
	}

	stats, err := projectCommentsService.GetProjectRatingStats(c.Request.Context(), uint(projectID))
	if err != nil {
		global.GVA_LOG.Error("获取项目评分统计失败!", zap.Error(err))
		response.FailWithMessage("获取项目评分统计失败: "+err.Error(), c)
		return
	}

	response.OkWithData(stats, c)
}

// GetProjectCommentByID 根据ID获取评论详情
// @Tags ProjectComments
// @Summary 根据ID获取评论详情
// @Accept application/json
// @Produce application/json
// @Param commentID path uint true "评论ID"
// @Success 200 {object} response.Response{data=mcp.ProjectComments,msg=string} "获取成功"
// @Router /projectComments/getProjectComment/{commentID} [get]
func (api *ProjectCommentsApi) GetProjectCommentByID(c *gin.Context) {
	commentIDStr := c.Param("commentID")
	commentID, err := strconv.ParseUint(commentIDStr, 10, 64)
	if err != nil {
		response.FailWithMessage("无效的评论ID", c)
		return
	}

	comment, err := projectCommentsService.GetProjectCommentByID(c.Request.Context(), uint(commentID))
	if err != nil {
		global.GVA_LOG.Error("获取评论详情失败!", zap.Error(err))
		response.FailWithMessage("获取评论详情失败: "+err.Error(), c)
		return
	}

	response.OkWithData(comment, c)
}

// CheckUserCommentPermission 检查用户评论权限
// @Tags ProjectComments
// @Summary 检查用户评论权限
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param projectID path uint true "项目ID"
// @Success 200 {object} response.Response{data=object,msg=string} "检查成功"
// @Router /projectComments/checkCommentPermission/{projectID} [get]
func (api *ProjectCommentsApi) CheckUserCommentPermission(c *gin.Context) {
	projectIDStr := c.Param("projectID")
	projectID, err := strconv.ParseUint(projectIDStr, 10, 64)
	if err != nil {
		response.FailWithMessage("无效的项目ID", c)
		return
	}

	// 获取当前用户ID
	userId := utils.GetUserID(c)
	if userId == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	// 检查评论权限
	err = projectCommentsService.CheckUserCommentPermission(c.Request.Context(), uint(projectID), userId)
	canComment := err == nil

	response.OkWithData(map[string]interface{}{
		"canComment": canComment,
		"message":    func() string { if err != nil { return err.Error() } else { return "可以评论" } }(),
	}, c)
}

// GetPendingCommentsList 获取待审核评论列表（管理员功能）
// @Tags ProjectComments
// @Summary 获取待审核评论列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.PendingCommentsListRequest true "获取待审核评论列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /projectComments/getPendingCommentsList [post]
func (api *ProjectCommentsApi) GetPendingCommentsList(c *gin.Context) {
	var req request.PendingCommentsListRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 获取待审核评论列表
	list, total, err := projectCommentsService.GetPendingCommentsList(c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("获取待审核评论列表失败!", zap.Error(err))
		response.FailWithMessage("获取待审核评论列表失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}, "获取成功", c)
}

// ReviewComment 审核评论（管理员功能）
// @Tags ProjectComments
// @Summary 审核评论
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.ReviewCommentRequest true "审核评论"
// @Success 200 {object} response.Response{msg=string} "审核成功"
// @Router /projectComments/reviewComment [post]
func (api *ProjectCommentsApi) ReviewComment(c *gin.Context) {
	var req request.ReviewCommentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID（审核人）
	reviewerId := utils.GetUserID(c)
	if reviewerId == 0 {
		response.FailWithMessage("用户未登录", c)
		return
	}

	// 执行审核
	if err := projectCommentsService.ReviewComment(c.Request.Context(), &req, reviewerId); err != nil {
		global.GVA_LOG.Error("审核评论失败!", zap.Error(err))
		response.FailWithMessage("审核失败: "+err.Error(), c)
		return
	}

	actionText := "通过"
	if req.Action == "reject" {
		actionText = "拒绝"
	}
	response.OkWithMessage("评论审核"+actionText+"成功", c)
}
