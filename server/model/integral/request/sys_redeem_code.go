package request

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/integral"
)

// CreateRedeemCodeRequest 创建兑换码请求
type CreateRedeemCodeRequest struct {
	Type        string              `json:"type" binding:"required,oneof=points membership vip custom" label:"兑换类型"` // 兑换类型
	Title       string              `json:"title" binding:"required,max=100" label:"兑换码标题"`                          // 兑换码标题
	Description string              `json:"description" binding:"max=200" label:"描述"`                                // 描述
	RewardData  integral.RewardData `json:"rewardData" binding:"required" label:"奖励数据"`                              // 奖励数据
	MaxUses     int                 `json:"maxUses" binding:"min=0" label:"最大使用次数"`                                 // 最大使用次数，0表示无限制
	ExpiresAt   *time.Time          `json:"expiresAt" label:"过期时间"`                                                  // 过期时间
	Remark      string              `json:"remark" binding:"max=500" label:"备注"`                                     // 备注
}

// BatchCreateRedeemCodeRequest 批量创建兑换码请求
type BatchCreateRedeemCodeRequest struct {
	Type        string              `json:"type" binding:"required,oneof=points membership vip custom" label:"兑换类型"` // 兑换类型
	Title       string              `json:"title" binding:"required,max=100" label:"兑换码标题"`                          // 兑换码标题
	Description string              `json:"description" binding:"max=200" label:"描述"`                                // 描述
	RewardData  integral.RewardData `json:"rewardData" binding:"required" label:"奖励数据"`                              // 奖励数据
	MaxUses     int                 `json:"maxUses" binding:"min=0" label:"最大使用次数"`                                 // 最大使用次数，0表示无限制
	ExpiresAt   *time.Time          `json:"expiresAt" label:"过期时间"`                                                  // 过期时间
	Count       int                 `json:"count" binding:"required,min=1,max=1000" label:"生成数量"`                    // 生成数量，最多1000个
	Remark      string              `json:"remark" binding:"max=500" label:"备注"`                                     // 备注
}

// UpdateRedeemCodeRequest 更新兑换码请求
type UpdateRedeemCodeRequest struct {
	ID          uint                `json:"id" binding:"required" label:"兑换码ID"`                                     // 兑换码ID
	Type        string              `json:"type" binding:"required,oneof=points membership vip custom" label:"兑换类型"` // 兑换类型
	Title       string              `json:"title" binding:"required,max=100" label:"兑换码标题"`                          // 兑换码标题
	Description string              `json:"description" binding:"max=200" label:"描述"`                                // 描述
	RewardData  integral.RewardData `json:"rewardData" binding:"required" label:"奖励数据"`                              // 奖励数据
	MaxUses     int                 `json:"maxUses" binding:"min=0" label:"最大使用次数"`                                 // 最大使用次数
	ExpiresAt   *time.Time          `json:"expiresAt" label:"过期时间"`                                                  // 过期时间
	IsActive    bool                `json:"isActive" label:"是否激活"`                                                   // 是否激活
	Remark      string              `json:"remark" binding:"max=500" label:"备注"`                                     // 备注
}

// RedeemCodeSearch 兑换码查询参数
type RedeemCodeSearch struct {
	request.PageInfo
	Code      string `json:"code" form:"code" label:"兑换码"`               // 兑换码
	Type      string `json:"type" form:"type" label:"兑换类型"`              // 兑换类型
	BatchID   string `json:"batchId" form:"batchId" label:"批次ID"`         // 批次ID
	IsActive  *bool  `json:"isActive" form:"isActive" label:"是否激活"`       // 是否激活
	CreatedBy uint   `json:"createdBy" form:"createdBy" label:"创建者ID"`    // 创建者ID
	StartDate string `json:"startDate" form:"startDate" label:"开始日期"`    // 开始日期
	EndDate   string `json:"endDate" form:"endDate" label:"结束日期"`        // 结束日期
	Status    string `json:"status" form:"status" label:"状态"`            // 状态：active/inactive/expired/exhausted
}

// UseRedeemCodeRequest 使用兑换码请求
type UseRedeemCodeRequest struct {
	Code string `json:"code" binding:"required,min=6,max=50" label:"兑换码"` // 兑换码
}

// RedeemCodeUsageSearch 兑换码使用记录查询参数
type RedeemCodeUsageSearch struct {
	request.PageInfo
	CodeID    uint   `json:"codeId" form:"codeId" label:"兑换码ID"`         // 兑换码ID
	UserID    uint   `json:"userId" form:"userId" label:"用户ID"`          // 用户ID
	Type      string `json:"type" form:"type" label:"兑换类型"`             // 兑换类型
	StartDate string `json:"startDate" form:"startDate" label:"开始日期"`   // 开始日期
	EndDate   string `json:"endDate" form:"endDate" label:"结束日期"`       // 结束日期
}
